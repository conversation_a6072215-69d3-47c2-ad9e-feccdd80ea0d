-- 故障申报表
CREATE TABLE `fault_report` (
  `fault_id` varchar(50) NOT NULL COMMENT '故障申报ID',
  `fault_title` varchar(200) NOT NULL COMMENT '故障标题',
  `fault_description` text COMMENT '故障描述',
  `asset_id` varchar(50) NOT NULL COMMENT '资产ID',
  `fault_type` tinyint(4) NOT NULL COMMENT '故障类型(1-电气故障, 2-机械故障, 3-控制系统故障, 4-安全故障, 5-其他故障)',
  `urgency_level` tinyint(4) NOT NULL COMMENT '紧急程度(1-一般, 2-较急, 3-紧急, 4-特急)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '故障状态(1-已提交, 2-已受理, 3-处理中, 4-已完成, 5-已关闭)',
  `report_location` varchar(200) DEFAULT NULL COMMENT '申报位置',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `report_time` datetime NOT NULL COMMENT '申报时间',
  `reporter_id` bigint(20) NOT NULL COMMENT '申报人ID',
  `handler_id` bigint(20) DEFAULT NULL COMMENT '处理人ID',
  `accept_time` datetime DEFAULT NULL COMMENT '受理时间',
  `estimated_complete_time` datetime DEFAULT NULL COMMENT '预计完成时间',
  `actual_complete_time` datetime DEFAULT NULL COMMENT '实际完成时间',
  `handle_description` text COMMENT '处理描述',
  `images` text COMMENT '故障图片（JSON数组格式存储）',
  `task_id` varchar(50) DEFAULT NULL COMMENT '关联的维护任务ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) NOT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除(0-否, 1-是)',
  PRIMARY KEY (`fault_id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_reporter_id` (`reporter_id`),
  KEY `idx_handler_id` (`handler_id`),
  KEY `idx_status` (`status`),
  KEY `idx_urgency_level` (`urgency_level`),
  KEY `idx_fault_type` (`fault_type`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='故障申报表';

-- 插入测试数据
INSERT INTO `fault_report` VALUES 
('FT20250401001', '空调制冷效果差', '空调运行正常但制冷效果明显下降，室内温度无法降到设定温度', '**********', 2, 3, 3, '设备楼3层机房', '13800138000', '2025-04-01 09:30:00', 103, 105, '2025-04-01 10:00:00', '2025-04-01 16:00:00', NULL, '已检查制冷系统，发现冷凝器结垢严重，正在清洗处理', 'image1.jpg,image2.jpg', 'MT20250401003', NULL, '2025-04-01 09:30:00', '2025-04-01 10:30:00', '张三', '李工', 0),
('FT20250401002', '电梯异响', '电梯运行时有异常响声，疑似轴承问题', '**********', 2, 2, 2, '办公楼1层电梯间', '13800138001', '2025-04-01 14:20:00', 104, 106, '2025-04-01 14:30:00', '2025-04-01 18:00:00', NULL, '已安排检查，初步判断为导轨润滑不足', 'image3.jpg', 'MT20250401004', NULL, '2025-04-01 14:20:00', '2025-04-01 14:35:00', '李四', '王工', 0),
('FT20250401003', '消防报警器误报', '消防报警器频繁误报，影响正常工作', 'AS20230003', 4, 4, 1, '办公楼2层走廊', '13800138002', '2025-04-01 16:45:00', 105, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '可能是灰尘导致', '2025-04-01 16:45:00', NULL, '王五', NULL, 0);

-- 为故障申报功能添加权限菜单
INSERT INTO `sys_menu` VALUES 
(2100, '故障申报', 2000, 5, 'fault', 'maintenance/fault/index', '', 1, 0, 'C', '0', '0', 'maintenance:fault:view', 'bug', 'admin', '2025-04-01 10:00:00', '', NULL, '故障申报菜单');

-- 添加故障申报相关权限
INSERT INTO `sys_menu` VALUES 
(2101, '故障申报查询', 2100, 1, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:query', '#', 'admin', '2025-04-01 10:00:00', '', NULL, ''),
(2102, '故障申报新增', 2100, 2, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:add', '#', 'admin', '2025-04-01 10:00:00', '', NULL, ''),
(2103, '故障申报修改', 2100, 3, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:edit', '#', 'admin', '2025-04-01 10:00:00', '', NULL, ''),
(2104, '故障申报删除', 2100, 4, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:remove', '#', 'admin', '2025-04-01 10:00:00', '', NULL, ''),
(2105, '故障申报导出', 2100, 5, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:export', '#', 'admin', '2025-04-01 10:00:00', '', NULL, ''),
(2106, '故障申报列表', 2100, 6, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:list', '#', 'admin', '2025-04-01 10:00:00', '', NULL, ''),
(2107, '故障申报受理', 2100, 7, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:accept', '#', 'admin', '2025-04-01 10:00:00', '', NULL, ''),
(2108, '故障申报处理', 2100, 8, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:handle', '#', 'admin', '2025-04-01 10:00:00', '', NULL, ''),
(2109, '故障申报关闭', 2100, 9, '', '', '', 1, 0, 'F', '0', '0', 'maintenance:fault:close', '#', 'admin', '2025-04-01 10:00:00', '', NULL, '');
