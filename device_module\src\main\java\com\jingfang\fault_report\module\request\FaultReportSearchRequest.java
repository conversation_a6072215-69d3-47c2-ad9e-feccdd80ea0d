package com.jingfang.fault_report.module.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 故障申报查询请求
 */
@Data
public class FaultReportSearchRequest {
    
    /**
     * 故障标题（模糊查询）
     */
    private String faultTitle;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称（模糊查询）
     */
    private String assetName;
    
    /**
     * 故障类型列表
     */
    private List<Integer> faultTypeList;
    
    /**
     * 紧急程度列表
     */
    private List<Integer> urgencyLevelList;
    
    /**
     * 故障状态列表
     */
    private List<Integer> statusList;
    
    /**
     * 申报人ID
     */
    private Long reporterId;
    
    /**
     * 处理人ID
     */
    private Long handlerId;
    
    /**
     * 申报时间开始
     */
    private Date reportTimeStart;
    
    /**
     * 申报时间结束
     */
    private Date reportTimeEnd;
    
    /**
     * 受理时间开始
     */
    private Date acceptTimeStart;
    
    /**
     * 受理时间结束
     */
    private Date acceptTimeEnd;
    
    /**
     * 完成时间开始
     */
    private Date completeTimeStart;
    
    /**
     * 完成时间结束
     */
    private Date completeTimeEnd;
    
    /**
     * 是否查询我的申报（当前用户申报的故障）
     */
    private Boolean myReports;
    
    /**
     * 是否查询我处理的故障
     */
    private Boolean myHandling;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向（asc/desc）
     */
    private String orderDirection;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
