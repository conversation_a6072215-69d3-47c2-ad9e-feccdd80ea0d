package com.jingfang.fault_report.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 故障申报表
 * @TableName fault_report
 */
@TableName(value = "fault_report")
@Data
public class FaultReport implements Serializable {
    
    /**
     * 故障申报ID
     */
    @TableId(type = IdType.INPUT)
    private String faultId;
    
    /**
     * 故障标题
     */
    private String faultTitle;
    
    /**
     * 故障描述
     */
    private String faultDescription;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 故障类型(1-电气故障, 2-机械故障, 3-控制系统故障, 4-安全故障, 5-其他故障)
     */
    private Integer faultType;
    
    /**
     * 紧急程度(1-一般, 2-较急, 3-紧急, 4-特急)
     */
    private Integer urgencyLevel;
    
    /**
     * 故障状态(1-已提交, 2-已受理, 3-处理中, 4-已完成, 5-已关闭)
     */
    private Integer status;
    
    /**
     * 申报位置
     */
    private String reportLocation;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 申报时间
     */
    private Date reportTime;
    
    /**
     * 申报人ID
     */
    private Long reporterId;
    
    /**
     * 处理人ID
     */
    private Long handlerId;
    
    /**
     * 受理时间
     */
    private Date acceptTime;
    
    /**
     * 预计完成时间
     */
    private Date estimatedCompleteTime;
    
    /**
     * 实际完成时间
     */
    private Date actualCompleteTime;
    
    /**
     * 处理描述
     */
    private String handleDescription;
    
    /**
     * 故障图片（JSON数组格式存储）
     */
    private String images;
    
    /**
     * 关联的维护任务ID
     */
    private String taskId;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除(0-否, 1-是)
     */
    private Integer deleted;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
