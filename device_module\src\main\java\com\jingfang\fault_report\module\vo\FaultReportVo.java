package com.jingfang.fault_report.module.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 故障申报VO
 */
@Data
public class FaultReportVo {
    
    /**
     * 故障申报ID
     */
    private String faultId;
    
    /**
     * 故障标题
     */
    private String faultTitle;
    
    /**
     * 故障描述
     */
    private String faultDescription;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 资产编号
     */
    private String assetCode;
    
    /**
     * 资产位置
     */
    private String assetLocation;
    
    /**
     * 故障类型(1-电气故障, 2-机械故障, 3-控制系统故障, 4-安全故障, 5-其他故障)
     */
    private Integer faultType;
    
    /**
     * 故障类型名称
     */
    private String faultTypeName;
    
    /**
     * 紧急程度(1-一般, 2-较急, 3-紧急, 4-特急)
     */
    private Integer urgencyLevel;
    
    /**
     * 紧急程度名称
     */
    private String urgencyLevelName;
    
    /**
     * 故障状态(1-已提交, 2-已受理, 3-处理中, 4-已完成, 5-已关闭)
     */
    private Integer status;
    
    /**
     * 故障状态名称
     */
    private String statusName;
    
    /**
     * 申报位置
     */
    private String reportLocation;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 申报时间
     */
    private Date reportTime;
    
    /**
     * 申报人ID
     */
    private Long reporterId;
    
    /**
     * 申报人名称
     */
    private String reporterName;
    
    /**
     * 处理人ID
     */
    private Long handlerId;
    
    /**
     * 处理人名称
     */
    private String handlerName;
    
    /**
     * 受理时间
     */
    private Date acceptTime;
    
    /**
     * 预计完成时间
     */
    private Date estimatedCompleteTime;
    
    /**
     * 实际完成时间
     */
    private Date actualCompleteTime;
    
    /**
     * 处理描述
     */
    private String handleDescription;
    
    /**
     * 故障图片列表
     */
    private List<String> images;
    
    /**
     * 关联的维护任务ID
     */
    private String taskId;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
}
