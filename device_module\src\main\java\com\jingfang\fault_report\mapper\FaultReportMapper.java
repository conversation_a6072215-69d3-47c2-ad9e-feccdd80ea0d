package com.jingfang.fault_report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.fault_report.module.entity.FaultReport;
import com.jingfang.fault_report.module.request.FaultReportSearchRequest;
import com.jingfang.fault_report.module.vo.FaultReportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 故障申报Mapper接口
 */
@Mapper
public interface FaultReportMapper extends BaseMapper<FaultReport> {
    
    /**
     * 分页查询故障申报
     */
    IPage<FaultReportVo> selectFaultReportPage(Page<FaultReportVo> page, 
                                              @Param("request") FaultReportSearchRequest request, 
                                              @Param("currentUserId") Long currentUserId);
    
    /**
     * 根据ID查询故障申报详情
     */
    FaultReportVo selectFaultReportById(@Param("faultId") String faultId);
    
    /**
     * 查询用户的故障申报列表
     */
    List<FaultReportVo> selectFaultReportsByUser(@Param("userId") Long userId, 
                                                @Param("statusList") List<Integer> statusList);
    
    /**
     * 查询处理人的故障申报列表
     */
    List<FaultReportVo> selectFaultReportsByHandler(@Param("handlerId") Long handlerId, 
                                                   @Param("statusList") List<Integer> statusList);
    
    /**
     * 查询紧急故障申报
     */
    List<FaultReportVo> selectUrgentFaultReports(@Param("urgencyLevel") Integer urgencyLevel);
    
    /**
     * 查询未处理的故障申报
     */
    List<FaultReportVo> selectUnhandledFaultReports();
    
    /**
     * 根据资产ID查询故障申报
     */
    List<FaultReportVo> selectFaultReportsByAssetId(@Param("assetId") String assetId);
    
    /**
     * 统计故障申报数量
     */
    Integer countFaultReportsByStatus(@Param("status") Integer status, 
                                     @Param("userId") Long userId);
}
