package com.jingfang.fault_report.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.fault_report.mapper.FaultReportMapper;
import com.jingfang.fault_report.module.dto.FaultReportDto;
import com.jingfang.fault_report.module.entity.FaultReport;
import com.jingfang.fault_report.module.request.FaultReportSearchRequest;
import com.jingfang.fault_report.module.vo.FaultReportVo;
import com.jingfang.fault_report.service.FaultReportService;
import com.jingfang.maintenance_task.module.dto.MaintenanceTaskDto;
import com.jingfang.maintenance_task.module.service.MaintenanceTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 故障申报服务实现类
 */
@Slf4j
@Service
public class FaultReportServiceImpl extends ServiceImpl<FaultReportMapper, FaultReport> implements FaultReportService {
    
    @Autowired
    private FaultReportMapper faultReportMapper;
    
    @Autowired
    private MaintenanceTaskService maintenanceTaskService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> submitFaultReport(FaultReportDto faultReportDto) {
        // 生成故障申报ID
        String faultId = IdUtils.fastSimpleUUID();
        
        // 转换为实体类
        FaultReport faultReport = faultReportDto.toEntity();
        faultReport.setFaultId(faultId);
        faultReport.setStatus(1); // 已提交
        faultReport.setReportTime(new Date());
        faultReport.setReporterId(SecurityUtils.getUserId());
        faultReport.setCreateTime(new Date());
        faultReport.setCreateBy(SecurityUtils.getUsername());
        faultReport.setDeleted(0);
        
        // 处理图片列表
        if (!CollectionUtils.isEmpty(faultReportDto.getImages())) {
            faultReport.setImages(String.join(",", faultReportDto.getImages()));
        }
        
        // 保存故障申报
        this.save(faultReport);
        
        // 根据紧急程度自动创建维护任务
        String taskId = createMaintenanceTaskFromFault(faultReport);
        
        // 更新故障申报的关联任务ID
        if (taskId != null) {
            faultReport.setTaskId(taskId);
            this.updateById(faultReport);
        }
        
        Map<String, String> result = new HashMap<>();
        result.put("faultId", faultId);
        result.put("taskId", taskId);
        
        return result;
    }
    
    /**
     * 根据故障申报创建维护任务
     */
    private String createMaintenanceTaskFromFault(FaultReport faultReport) {
        try {
            MaintenanceTaskDto taskDto = new MaintenanceTaskDto();
            taskDto.setTaskTitle("故障处理：" + faultReport.getFaultTitle());
            taskDto.setAssetId(faultReport.getAssetId());
            taskDto.setMaintenanceItems(faultReport.getFaultDescription());
            taskDto.setScheduledTime(new Date()); // 立即执行
            
            // 根据紧急程度设置任务优先级
            taskDto.setPriority(faultReport.getUrgencyLevel());
            
            // 设置负责人类型为个人（后续可以通过规则引擎自动分配）
            taskDto.setResponsibleType(1);
            // TODO: 这里可以根据故障类型和资产信息自动分配处理人
            // 暂时设置为当前用户
            taskDto.setResponsibleId(SecurityUtils.getUserId());
            
            return maintenanceTaskService.addTask(taskDto);
        } catch (Exception e) {
            log.error("根据故障申报创建维护任务失败", e);
            return null;
        }
    }
    
    @Override
    public IPage<FaultReportVo> getFaultReportPage(FaultReportSearchRequest request) {
        Page<FaultReportVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        Long currentUserId = SecurityUtils.getUserId();
        return faultReportMapper.selectFaultReportPage(page, request, currentUserId);
    }
    
    @Override
    public FaultReportVo getFaultReportById(String faultId) {
        FaultReportVo faultReportVo = faultReportMapper.selectFaultReportById(faultId);
        if (faultReportVo != null && faultReportVo.getImages() != null) {
            // 将图片字符串转换为列表
            String[] imageArray = faultReportVo.getImages().toString().split(",");
            faultReportVo.setImages(Arrays.asList(imageArray));
        }
        return faultReportVo;
    }
    
    @Override
    public List<FaultReportVo> getMyFaultReports(List<Integer> statusList) {
        Long currentUserId = SecurityUtils.getUserId();
        return faultReportMapper.selectFaultReportsByUser(currentUserId, statusList);
    }
    
    @Override
    public List<FaultReportVo> getMyHandlingFaultReports(List<Integer> statusList) {
        Long currentUserId = SecurityUtils.getUserId();
        return faultReportMapper.selectFaultReportsByHandler(currentUserId, statusList);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean acceptFaultReport(String faultId, Long handlerId, String handleDescription) {
        FaultReport faultReport = this.getById(faultId);
        if (faultReport == null) {
            return false;
        }
        
        faultReport.setStatus(2); // 已受理
        faultReport.setHandlerId(handlerId);
        faultReport.setAcceptTime(new Date());
        faultReport.setHandleDescription(handleDescription);
        faultReport.setUpdateTime(new Date());
        faultReport.setUpdateBy(SecurityUtils.getUsername());
        
        return this.updateById(faultReport);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFaultProgress(String faultId, String handleDescription) {
        FaultReport faultReport = this.getById(faultId);
        if (faultReport == null) {
            return false;
        }
        
        faultReport.setStatus(3); // 处理中
        faultReport.setHandleDescription(handleDescription);
        faultReport.setUpdateTime(new Date());
        faultReport.setUpdateBy(SecurityUtils.getUsername());
        
        return this.updateById(faultReport);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeFaultReport(String faultId, String handleDescription) {
        FaultReport faultReport = this.getById(faultId);
        if (faultReport == null) {
            return false;
        }
        
        faultReport.setStatus(4); // 已完成
        faultReport.setActualCompleteTime(new Date());
        faultReport.setHandleDescription(handleDescription);
        faultReport.setUpdateTime(new Date());
        faultReport.setUpdateBy(SecurityUtils.getUsername());
        
        return this.updateById(faultReport);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean closeFaultReport(String faultId, String closeReason) {
        FaultReport faultReport = this.getById(faultId);
        if (faultReport == null) {
            return false;
        }
        
        faultReport.setStatus(5); // 已关闭
        faultReport.setRemark(closeReason);
        faultReport.setUpdateTime(new Date());
        faultReport.setUpdateBy(SecurityUtils.getUsername());
        
        return this.updateById(faultReport);
    }
    
    @Override
    public List<FaultReportVo> getUrgentFaultReports() {
        return faultReportMapper.selectUrgentFaultReports(3); // 紧急及以上
    }
    
    @Override
    public List<FaultReportVo> getUnhandledFaultReports() {
        return faultReportMapper.selectUnhandledFaultReports();
    }
    
    @Override
    public List<FaultReportVo> getFaultReportsByAssetId(String assetId) {
        return faultReportMapper.selectFaultReportsByAssetId(assetId);
    }
    
    @Override
    public Map<String, Integer> getFaultReportStatistics() {
        Long currentUserId = SecurityUtils.getUserId();
        Map<String, Integer> statistics = new HashMap<>();
        
        statistics.put("submitted", faultReportMapper.countFaultReportsByStatus(1, currentUserId));
        statistics.put("accepted", faultReportMapper.countFaultReportsByStatus(2, currentUserId));
        statistics.put("processing", faultReportMapper.countFaultReportsByStatus(3, currentUserId));
        statistics.put("completed", faultReportMapper.countFaultReportsByStatus(4, currentUserId));
        statistics.put("closed", faultReportMapper.countFaultReportsByStatus(5, currentUserId));
        
        return statistics;
    }
    
    @Override
    public List<Map<String, Object>> getFaultTypeOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        options.add(createOption(1, "电气故障"));
        options.add(createOption(2, "机械故障"));
        options.add(createOption(3, "控制系统故障"));
        options.add(createOption(4, "安全故障"));
        options.add(createOption(5, "其他故障"));
        return options;
    }
    
    @Override
    public List<Map<String, Object>> getUrgencyLevelOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        options.add(createUrgencyOption(1, "一般", "不影响正常使用"));
        options.add(createUrgencyOption(2, "较急", "影响部分功能"));
        options.add(createUrgencyOption(3, "紧急", "严重影响使用"));
        options.add(createUrgencyOption(4, "特急", "存在安全隐患"));
        return options;
    }
    
    private Map<String, Object> createOption(Integer value, String label) {
        Map<String, Object> option = new HashMap<>();
        option.put("value", value);
        option.put("label", label);
        return option;
    }
    
    private Map<String, Object> createUrgencyOption(Integer value, String label, String description) {
        Map<String, Object> option = new HashMap<>();
        option.put("value", value);
        option.put("label", label);
        option.put("description", description);
        return option;
    }
}
