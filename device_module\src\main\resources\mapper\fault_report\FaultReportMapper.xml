<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.fault_report.mapper.FaultReportMapper">

    <resultMap id="FaultReportResult" type="com.jingfang.fault_report.module.entity.FaultReport">
        <id property="faultId" column="fault_id"/>
        <result property="faultTitle" column="fault_title"/>
        <result property="faultDescription" column="fault_description"/>
        <result property="assetId" column="asset_id"/>
        <result property="faultType" column="fault_type"/>
        <result property="urgencyLevel" column="urgency_level"/>
        <result property="status" column="status"/>
        <result property="reportLocation" column="report_location"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="reportTime" column="report_time"/>
        <result property="reporterId" column="reporter_id"/>
        <result property="handlerId" column="handler_id"/>
        <result property="acceptTime" column="accept_time"/>
        <result property="estimatedCompleteTime" column="estimated_complete_time"/>
        <result property="actualCompleteTime" column="actual_complete_time"/>
        <result property="handleDescription" column="handle_description"/>
        <result property="images" column="images"/>
        <result property="taskId" column="task_id"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <resultMap id="FaultReportVoResult" type="com.jingfang.fault_report.module.vo.FaultReportVo">
        <id property="faultId" column="fault_id"/>
        <result property="faultTitle" column="fault_title"/>
        <result property="faultDescription" column="fault_description"/>
        <result property="assetId" column="asset_id"/>
        <result property="assetName" column="asset_name"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetLocation" column="asset_location"/>
        <result property="faultType" column="fault_type"/>
        <result property="faultTypeName" column="fault_type_name"/>
        <result property="urgencyLevel" column="urgency_level"/>
        <result property="urgencyLevelName" column="urgency_level_name"/>
        <result property="status" column="status"/>
        <result property="statusName" column="status_name"/>
        <result property="reportLocation" column="report_location"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="reportTime" column="report_time"/>
        <result property="reporterId" column="reporter_id"/>
        <result property="reporterName" column="reporter_name"/>
        <result property="handlerId" column="handler_id"/>
        <result property="handlerName" column="handler_name"/>
        <result property="acceptTime" column="accept_time"/>
        <result property="estimatedCompleteTime" column="estimated_complete_time"/>
        <result property="actualCompleteTime" column="actual_complete_time"/>
        <result property="handleDescription" column="handle_description"/>
        <result property="images" column="images"/>
        <result property="taskId" column="task_id"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectFaultReportVo">
        select 
            fr.fault_id, fr.fault_title, fr.fault_description, fr.asset_id, fr.fault_type, fr.urgency_level,
            fr.status, fr.report_location, fr.contact_phone, fr.report_time, fr.reporter_id, fr.handler_id,
            fr.accept_time, fr.estimated_complete_time, fr.actual_complete_time, fr.handle_description,
            fr.images, fr.task_id, fr.remark, fr.create_time, fr.update_time, fr.create_by, fr.update_by,
            a.asset_name, a.asset_code, a.location as asset_location,
            ru.nick_name as reporter_name,
            hu.nick_name as handler_name,
            case fr.fault_type
                when 1 then '电气故障'
                when 2 then '机械故障'
                when 3 then '控制系统故障'
                when 4 then '安全故障'
                when 5 then '其他故障'
                else '未知'
            end as fault_type_name,
            case fr.urgency_level
                when 1 then '一般'
                when 2 then '较急'
                when 3 then '紧急'
                when 4 then '特急'
                else '未知'
            end as urgency_level_name,
            case fr.status
                when 1 then '已提交'
                when 2 then '已受理'
                when 3 then '处理中'
                when 4 then '已完成'
                when 5 then '已关闭'
                else '未知'
            end as status_name
        from fault_report fr
        left join asset a on fr.asset_id = a.asset_id
        left join sys_user ru on fr.reporter_id = ru.user_id
        left join sys_user hu on fr.handler_id = hu.user_id
        where fr.deleted = 0
    </sql>

    <select id="selectFaultReportPage" resultMap="FaultReportVoResult">
        <include refid="selectFaultReportVo"/>
        <where>
            <if test="request.faultTitle != null and request.faultTitle != ''">
                and fr.fault_title like concat('%', #{request.faultTitle}, '%')
            </if>
            <if test="request.assetId != null and request.assetId != ''">
                and fr.asset_id = #{request.assetId}
            </if>
            <if test="request.assetName != null and request.assetName != ''">
                and a.asset_name like concat('%', #{request.assetName}, '%')
            </if>
            <if test="request.faultTypeList != null and request.faultTypeList.size() > 0">
                and fr.fault_type in
                <foreach collection="request.faultTypeList" item="faultType" open="(" separator="," close=")">
                    #{faultType}
                </foreach>
            </if>
            <if test="request.urgencyLevelList != null and request.urgencyLevelList.size() > 0">
                and fr.urgency_level in
                <foreach collection="request.urgencyLevelList" item="urgencyLevel" open="(" separator="," close=")">
                    #{urgencyLevel}
                </foreach>
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                and fr.status in
                <foreach collection="request.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.reporterId != null">
                and fr.reporter_id = #{request.reporterId}
            </if>
            <if test="request.handlerId != null">
                and fr.handler_id = #{request.handlerId}
            </if>
            <if test="request.reportTimeStart != null">
                and fr.report_time >= #{request.reportTimeStart}
            </if>
            <if test="request.reportTimeEnd != null">
                and fr.report_time &lt;= #{request.reportTimeEnd}
            </if>
            <if test="request.acceptTimeStart != null">
                and fr.accept_time >= #{request.acceptTimeStart}
            </if>
            <if test="request.acceptTimeEnd != null">
                and fr.accept_time &lt;= #{request.acceptTimeEnd}
            </if>
            <if test="request.completeTimeStart != null">
                and fr.actual_complete_time >= #{request.completeTimeStart}
            </if>
            <if test="request.completeTimeEnd != null">
                and fr.actual_complete_time &lt;= #{request.completeTimeEnd}
            </if>
            <if test="request.myReports != null and request.myReports == true">
                and fr.reporter_id = #{currentUserId}
            </if>
            <if test="request.myHandling != null and request.myHandling == true">
                and fr.handler_id = #{currentUserId}
            </if>
        </where>
        order by 
        <choose>
            <when test="request.orderBy != null and request.orderBy != ''">
                ${request.orderBy}
                <if test="request.orderDirection != null and request.orderDirection != ''">
                    ${request.orderDirection}
                </if>
            </when>
            <otherwise>
                fr.report_time desc
            </otherwise>
        </choose>
    </select>

    <select id="selectFaultReportById" resultMap="FaultReportVoResult">
        <include refid="selectFaultReportVo"/>
        and fr.fault_id = #{faultId}
    </select>

    <select id="selectFaultReportsByUser" resultMap="FaultReportVoResult">
        <include refid="selectFaultReportVo"/>
        and fr.reporter_id = #{userId}
        <if test="statusList != null and statusList.size() > 0">
            and fr.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        order by fr.report_time desc
    </select>

    <select id="selectFaultReportsByHandler" resultMap="FaultReportVoResult">
        <include refid="selectFaultReportVo"/>
        and fr.handler_id = #{handlerId}
        <if test="statusList != null and statusList.size() > 0">
            and fr.status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        order by fr.report_time desc
    </select>

    <select id="selectUrgentFaultReports" resultMap="FaultReportVoResult">
        <include refid="selectFaultReportVo"/>
        and fr.urgency_level >= #{urgencyLevel}
        and fr.status in (1, 2, 3)
        order by fr.urgency_level desc, fr.report_time asc
    </select>

    <select id="selectUnhandledFaultReports" resultMap="FaultReportVoResult">
        <include refid="selectFaultReportVo"/>
        and fr.status = 1
        order by fr.urgency_level desc, fr.report_time asc
    </select>

    <select id="selectFaultReportsByAssetId" resultMap="FaultReportVoResult">
        <include refid="selectFaultReportVo"/>
        and fr.asset_id = #{assetId}
        order by fr.report_time desc
    </select>

    <select id="countFaultReportsByStatus" resultType="java.lang.Integer">
        select count(*)
        from fault_report
        where deleted = 0
        and status = #{status}
        <if test="userId != null">
            and reporter_id = #{userId}
        </if>
    </select>

</mapper>
