package com.jingfang.fault_report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.fault_report.module.dto.FaultReportDto;
import com.jingfang.fault_report.module.entity.FaultReport;
import com.jingfang.fault_report.module.request.FaultReportSearchRequest;
import com.jingfang.fault_report.module.vo.FaultReportVo;

import java.util.List;
import java.util.Map;

/**
 * 故障申报服务接口
 */
public interface FaultReportService extends IService<FaultReport> {
    
    /**
     * 提交故障申报
     */
    Map<String, String> submitFaultReport(FaultReportDto faultReportDto);
    
    /**
     * 分页查询故障申报
     */
    IPage<FaultReportVo> getFaultReportPage(FaultReportSearchRequest request);
    
    /**
     * 根据ID查询故障申报详情
     */
    FaultReportVo getFaultReportById(String faultId);
    
    /**
     * 查询我的故障申报
     */
    List<FaultReportVo> getMyFaultReports(List<Integer> statusList);
    
    /**
     * 查询我处理的故障申报
     */
    List<FaultReportVo> getMyHandlingFaultReports(List<Integer> statusList);
    
    /**
     * 受理故障申报
     */
    boolean acceptFaultReport(String faultId, Long handlerId, String handleDescription);
    
    /**
     * 更新故障处理进度
     */
    boolean updateFaultProgress(String faultId, String handleDescription);
    
    /**
     * 完成故障处理
     */
    boolean completeFaultReport(String faultId, String handleDescription);
    
    /**
     * 关闭故障申报
     */
    boolean closeFaultReport(String faultId, String closeReason);
    
    /**
     * 查询紧急故障申报
     */
    List<FaultReportVo> getUrgentFaultReports();
    
    /**
     * 查询未处理的故障申报
     */
    List<FaultReportVo> getUnhandledFaultReports();
    
    /**
     * 根据资产ID查询故障申报
     */
    List<FaultReportVo> getFaultReportsByAssetId(String assetId);
    
    /**
     * 获取故障申报统计信息
     */
    Map<String, Integer> getFaultReportStatistics();
    
    /**
     * 获取故障类型选项
     */
    List<Map<String, Object>> getFaultTypeOptions();
    
    /**
     * 获取紧急程度选项
     */
    List<Map<String, Object>> getUrgencyLevelOptions();
}
